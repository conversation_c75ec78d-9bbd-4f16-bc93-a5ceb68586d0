'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { revalidatePath } from 'next/cache';
import { getLogger } from '@kit/shared/logger';
import { getUniqueId } from '~/services/utils';

// LinkedIn interfaces have been removed - no longer needed

// LinkedIn integration has been removed - this function is no longer needed

// LinkedIn integration functions have been removed - no longer needed

/**
 * Create a new social profile using Ayrshare API
 */
export const createProfile = async (userId: string, companyId: string, title?: string) => {
  const logger = await getLogger();
  
  try { 
    logger.info('Creating social profile', { userId, companyId, title });

    const response = await fetch("https://api.ayrshare.com/api/profiles", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.AYRSHARE_API_KEY}`
      },
      body: JSON.stringify({
        title: getUniqueId()
      }),
    });
    
    const responseData = await response.json();
    logger.info('Ayrshare API response', { 
      status: responseData.status, 
      profileKey: responseData.profileKey,
      refId: responseData.refId 
    });

    if (responseData.status === 'success') {
      // Store the profile in the database immediately to ensure webhook can find it
      const supabase = getSupabaseServerClient();
      const { data: dbProfile, error: dbError } = await supabase
        .from('ayrshare_user_profile')
        .insert({
          user_id: userId,
          company_id: companyId,
          title: responseData.title,
          refId: responseData.refId,
          profileKey: responseData.profileKey,
          messagingActive: responseData.messagingActive,
          profile_name: title || 'Default Profile',
          is_shared: false,
          permissions: { can_post: false, can_view: true, can_analytics: false }
        })
        .select()
        .single();

      if (dbError) {
        logger.error('Database error creating profile', { error: dbError });
        return {
          status: 'error',
          error: 'Failed to save profile to database'
        };
      }

      logger.info('Profile created successfully in database', { 
        profileId: dbProfile.id,
        refId: responseData.refId 
      });

      // Return the data from our database which includes the profileKey
      return {
        status: 'success',
        profileKey: responseData.profileKey,
        refId: responseData.refId,
        title: responseData.title,
        profileName: title || 'Default Profile',
        messagingActive: responseData.messagingActive,
        profileId: dbProfile.id
      };
    } else {
      console.error('Ayrshare API error', { responseData });
      logger.error('Ayrshare API error', { responseData });
      return {
        status: 'error',
        error: 'Failed to create profile with Ayrshare API'
      };
    }
  } catch (error) {
    logger.error('Error creating profile', { error });
    return {
      status: 'error',
      error: 'Internal server error'
    };
  }
};

export const generateJWT = async (profileKey: string) => {
  console.log("profileKey", {profileKey});
  const decodedPrivateKey = Buffer.from(process.env.AYRSHARE_PRIVATE_KEY || '', 'base64').toString('utf-8');
  
  try {
    const response = await fetch("https://api.ayrshare.com/api/profiles/generateJWT", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.AYRSHARE_API_KEY}`
      },
      body: JSON.stringify({
        domain: process.env.AYRSHARE_DOMAIN,
        privateKey: decodedPrivateKey,
        profileKey,
        logout: true
      }),
    });
    
    if (!response.ok) {
      throw new Error(`Ayrshare API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Check if the JWT token is expired
    if (data.jwt) {
      try {
        const payload = JSON.parse(atob(data.jwt.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);
        
        if (payload.exp && payload.exp < currentTime) {
          return {
            status: 'error',
            message: 'The connection token has expired. Please try again.',
            expired: true
          };
        }
      } catch (parseError) {
        console.error('Error parsing JWT payload:', parseError);
      }
    }
    
    return data;
  } catch (error) {
    console.error('Error generating JWT:', error);
    return {
      status: 'error',
      message: 'Failed to generate connection token. Please try again.',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

export const getSocialProfiles = async (userId: string, companyId: string) => {
  const logger = await getLogger();
  
  try {
    console.log('Fetching social profiles', { userId, companyId });

    const supabase = getSupabaseServerClient();
    const { data, error } = await (supabase as any)
      .from('ayrshare_user_profile')
      .select('*')
      // .eq('user_id', userId)
      .eq('company_id', companyId)
      .order('created_at', { ascending: false });

    console.log("data", {data});
    if (error) {
      logger.error('Database error fetching social profiles', { error });
      throw error;
    }

    logger.info('Social profiles fetched successfully', { count: data?.length || 0 });
    return data || [];
  } catch (error) {
    logger.error('Error fetching social profiles', { error });
    return [];
  }
};

export const getProfilesDetails = async (profileKey: string) => {
  const logger = await getLogger();
  
  try {
    logger.info('Fetching profile details', { profileKey });

    const displayInfoRaw = await fetch(`https://api.ayrshare.com/api/user`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.AYRSHARE_API_KEY}`,
        "Profile-Key": profileKey
      },
    });
    
    const displayInfo = await displayInfoRaw.json();
    console.log("displayInfo", {displayInfo});
    if (displayInfo.activeSocialAccounts) {
      logger.info('Profile details fetched successfully', { 
        accountCount: displayInfo.activeSocialAccounts?.length || 0 
      });
      return displayInfo;
    } else {
      logger.warn('No active social accounts found');
      return {};
    }
  } catch (error) {
    logger.error('Error fetching profile details', { error });
    return {};
  }
};

export const disconnectSocialProfile = async (platform: string, profileKey: string) => {
  const logger = await getLogger();
  
  try {
    logger.info('Disconnecting social profile', { platform, profileKey });

    const response = await fetch('https://api.ayrshare.com/api/profiles/social', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
        'Profile-Key': profileKey,
      },
      body: JSON.stringify({
        platform: platform
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to disconnect ${platform}: ${error}`);
    }

    const result = await response.json();
    logger.info('Social profile disconnected successfully', { platform });
    
    return result;
  } catch (error) {
    logger.error('Error disconnecting social profile', { error });
    throw error;
  }
};

/**
 * Delete a social profile
 */
export const deleteSocialProfile = async (profileId: string) => {
  const logger = await getLogger();
  
  try {
    logger.info('Deleting social profile', { profileId });

      logger.info('Disconnecting social profile', { profileId });
  
      const response = await fetch('https://api.ayrshare.com/api/profiles', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
          'Profile-Key': profileId,
        }
      });
  
      if (!response.ok) {
        const error = await response.text();
        throw new Error(`Failed to delete profile ${error}`);
      }
  
      const result = await response.json();
      logger.info('Social profile disconnected successfully', { profileId });

      return result;
  } catch (error) {
    console.log('Error deleting social profile', { error });
    return {
      success: false,
      error: 'Failed to delete profile'
    };
  }
};

/**
 * Register a webhook for a specific profile
 */
export const registerWebhook = async (profileKey: string, action: string = 'social') => {
  const logger = await getLogger();
  
  try {
    logger.info('Registering webhook', { profileKey, action });

    // Get the base URL from environment variables
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    const webhookUrl = `${baseUrl}/api/integrations/ayrshare/webhook`;
    // const webhookUrl = `https://webhook.site/44d131b4-9ccc-47e5-bc9c-b5259b93bbb8`;
    
    // Get the webhook secret from environment variables
    const webhookSecret = process.env.AYRSHARE_WEBHOOK_SECRET;

    const payload: any = {
      action: action,
      url: webhookUrl,
    };

    // Add secret if configured
    if (webhookSecret) {
      payload.secret = webhookSecret;
    }

    const response = await fetch('https://api.ayrshare.com/api/hook/webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
        'Profile-Key': profileKey,
      },
      body: JSON.stringify(payload),
    });

    const responseData = await response.json();

    if (!response.ok) {
      logger.error('Failed to register webhook', { 
        profileKey, 
        action, 
        status: response.status,
        error: responseData 
      });
      return {
        status: 'error',
        error: 'Failed to register webhook',
        details: responseData
      };
    }

    logger.info('Successfully registered webhook', { 
      profileKey, 
      action, 
      webhookUrl,
      refId: responseData.refId 
    });

    return {
      status: 'success',
      data: responseData
    };
  } catch (error) {
    logger.error('Error registering webhook', { error });
    return {
      status: 'error',
      error: 'Internal server error'
    };
  }
};

/**
 * Unregister a webhook for a specific profile
 */
export const unregisterWebhook = async (profileKey: string, action: string = 'social') => {
  const logger = await getLogger();
  
  try {
    logger.info('Unregistering webhook', { profileKey, action });

    const response = await fetch('https://api.ayrshare.com/api/hook/webhook', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.AYRSHARE_API_KEY}`,
        'Profile-Key': profileKey,
      },
      body: JSON.stringify({
        action: action,
      }),
    });

    const responseData = await response.json();

    if (!response.ok) {
      logger.error('Failed to unregister webhook', { 
        profileKey, 
        action, 
        status: response.status,
        error: responseData 
      });
      return {
        status: 'error',
        error: 'Failed to unregister webhook',
        details: responseData
      };
    }

    logger.info('Successfully unregistered webhook', { 
      profileKey, 
      action,
      refId: responseData.refId 
    });

    return {
      status: 'success',
      data: responseData
    };
  } catch (error) {
    logger.error('Error unregistering webhook', { error });
    return {
      status: 'error',
      error: 'Internal server error'
    };
  }
};