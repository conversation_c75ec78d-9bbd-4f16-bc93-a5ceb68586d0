'use client';

import { useState, useTransition } from 'react';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Trans } from '@kit/ui/trans';
import { toast } from '@kit/ui/sonner';
import { Plus } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { createProfile, generateJWT, registerWebhook } from '../_lib/server/server-actions';
import { useZero } from '~/hooks/use-zero';

const addProfileSchema = z.object({
  title: z.string().min(1, 'Profile title is required').max(100, 'Title must be less than 100 characters'),
});

type AddProfileFormData = z.infer<typeof addProfileSchema>;

interface AddSocialProfileDialogProps {
  onProfileAdded?: () => void;
}

export function AddSocialProfileDialog({ onProfileAdded }: AddSocialProfileDialogProps) {
  const [open, setOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();
  const form = useForm<AddProfileFormData>({
    resolver: zodResolver(addProfileSchema),
    defaultValues: {
      title: '',
    },
  });

  const onSubmit = (data: AddProfileFormData) => {
    startTransition(async () => {
      try {
        const result = await createProfile(workspace.user.id, workspace.account.id, data.title);
        console.log("result", result);
        
        // Profile is now created server-side, so we just need to refresh the Zero cache
        // The server action already handles database insertion

        if (result && result.status === 'success' && result.profileKey) {
          // Register webhook for the new profile
          try {
            const webhookResult = await registerWebhook(result.profileKey, 'social');
            
            if (webhookResult.status === 'success') {
              console.log('Webhook registered successfully for profile:', result.profileKey);
            } else {
              console.warn('Failed to register webhook for profile:', result.profileKey, webhookResult.error);
              // Don't fail the entire operation if webhook registration fails
            }
          } catch (webhookError) {
            console.error('Error registering webhook:', webhookError);
            // Don't fail the entire operation if webhook registration fails
          }
          
          toast.success(
            <Trans 
              i18nKey="integrations:socialProfile.createSuccess" 
              defaults="Social profile created successfully! You can now connect your social accounts." 
            />
          );
          
          setOpen(false);
          form.reset();
        } else {
          toast.error(
            <Trans 
              i18nKey="integrations:socialProfile.createError" 
              defaults="Failed to create social profile. Please try again." 
            />
          );
        }
      } catch (error) {
        console.error('Error creating social profile:', error);
        toast.error(
          <Trans 
            i18nKey="integrations:socialProfile.unexpectedError" 
            defaults="An unexpected error occurred. Please try again." 
          />
        );
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          <Trans i18nKey="integrations:socialProfile.addNew" defaults="Add Social Profile" />
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="integrations:socialProfile.addTitle" defaults="Add New Social Profile" />
          </DialogTitle>
          <DialogDescription>
            <Trans 
              i18nKey="integrations:socialProfile.addDescription" 
              defaults="Create a new social media profile to manage different sets of social accounts (e.g., Personal, Company, Brand)." 
            />
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="integrations:socialProfile.titleLabel" defaults="Profile Title" />
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Personal, Company, Brand A"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isPending}
              >
                <Trans i18nKey="common:cancel" defaults="Cancel" />
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? (
                  <Trans i18nKey="integrations:socialProfile.creating" defaults="Creating..." />
                ) : (
                  <Trans i18nKey="integrations:socialProfile.create" defaults="Create Profile" />
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 