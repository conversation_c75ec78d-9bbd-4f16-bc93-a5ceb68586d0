'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import { Skeleton } from '@kit/ui/skeleton';
import { Trans } from '@kit/ui/trans';
import { toast } from '@kit/ui/sonner';
import { MoreHorizontal, Share2, Users, Edit, Trash2, Lock } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@kit/ui/dropdown-menu';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { getSocialProfiles, getProfilesDetails, disconnectSocialProfile, deleteSocialProfile, generateJWT } from '../_lib/server/server-actions';
import { use<PERSON>ero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';

interface PlatformConnection {
  id: string;
  refId: string | null;
  platform: string | null;
  display_name: string | null;
  username: string | null;
  user_image: string | null;
  profile_url: string | null;
  headline?: string | null;
  subscription_type?: string | null;
  verified_type?: string | null;
  refresh_days_remaining?: number | null;
  refresh_required?: number | null;
  is_connected: boolean | null;
  connected_at: number | null;
  created_at: number | null;
  updated_at: number | null;
  post_history?: any;
}

const platformLogos = {
  linkedin: '/images/LI-Logo.png',
  twitter: '/images/logo-black.png',
  facebook: '/images/logo-black.png',
  instagram: '/images/logo-black.png',
};

const platformNames = {
  linkedin: 'LinkedIn',
  twitter: 'Twitter/X',
  facebook: 'Facebook',
  instagram: 'Instagram',
};

export function SocialProfilesList() {
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(false);
  const [disconnecting, setDisconnecting] = useState<string | null>(null);
  const zero = useZero();

  const [profiles] = useZeroQuery(
    zero.query.ayrshare_user_profile
    .where('company_id', '=', workspace.account.id),
    {
      ttl: '10m'
    }
  );

  const [platformConnections] = useZeroQuery(
    zero.query.ayrshare_social_profiles
    .where('company_id', '=', workspace.account.id), // ✅ FIXED: Re-enabled company filter
    {
      ttl: '5m'
    }
  );
  

  // const fetchProfiles = async () => {
  //   try {
  //     setLoading(true);
  //     // Note: Database updates are now handled by the webhook automatically
  //     // when users connect/disconnect their social accounts
      
  //     // Platform connections are now fetched directly from the database
  //     // No need to fetch from API since webhook keeps it updated
  //     console.log("Platform connections:", platformConnections);

  //     // FOR DEVELOPMENT: Test webhook processing locally
  //     try {
  //       console.log("Testing webhook locally...");
  //       const webhookResponse = await fetch('/api/integrations/ayrshare/webhook', {
  //         method: 'POST',
  //         headers: {
  //           'Content-Type': 'application/json',
  //         },
  //         body: JSON.stringify(
  //           {
  //             "action": "social",
  //             "created": "2025-07-13T07:24:10Z",
  //             "hookId": "1lImCnsE3ydwTOrEf3TB",
  //             "platform": "linkedin",
  //             "refId": "7a25881412a9ef431e9110c309c7f02333f150af",
  //             "source": "user",
  //             "timeStamp": **********,
  //             "title": "J4oOA9XD5l",
  //             "type": "link",
  //             "url": "https://webhook.site/44d131b4-9ccc-47e5-bc9c-b5259b93bbb8"
  //           }
  //       ),
  //       });
        
  //       if (webhookResponse.ok) {
  //         console.log("Webhook test successful!");
  //                    // Add a small delay to let the database update
  //          // The zero query should automatically update when data changes
  //          console.log("Database should update automatically via Zero sync");
  //       } else {
  //         console.error("Webhook test failed:", await webhookResponse.text());
  //       }
  //     } catch (webhookError) {
  //       console.error('Error testing webhook:', webhookError);
  //     }
      
  //   } catch (error) {
  //     console.error('Error fetching profiles:', error);
  //     toast.error('Failed to load social profiles');
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  useEffect(() => {
    // fetchProfiles();
  }, [workspace.user.id, workspace.account.id]);

  const handleConnectAccounts = async (profile: any) => {

    if (!profile.profileKey) {
      toast.error('Profile key not found. Please contact support, or delete this profile and try again');
      return;
    }

    try {
      console.log("profile", profile);
      const jwtData = await generateJWT(profile.profileKey);
      console.log("jwtData", jwtData);
      if (jwtData.status === 'success') {
        window.open(jwtData.url, '_blank');
        toast.success('Opening social accounts connection page...');
      } else {
        toast.error('Failed to open connection page');
      }
    } catch (error) {
      console.error('Error generating JWT:', error);
      toast.error('Failed to open connection page');
    }
  };

  const handleDisconnectAccount = async (connection: PlatformConnection) => {
    // Find the associated profile to get the profileKey
    const profile = profiles?.find((p: any) => p.refId === connection.refId);
    
    if (!profile?.profileKey) {
      toast.error('Profile key not found');
      return;
    }

    if (!connection.platform) {
      toast.error('Platform not specified');
      return;
    }

    setDisconnecting(`${connection.id}-${connection.platform}`);
    try {
      await disconnectSocialProfile(connection.platform, profile.profileKey);
      toast.success(`Disconnected ${connection.platform} successfully`);
      
      // Note: Database will be updated automatically by the webhook
      // No need to manually update state here
    } catch (error) {
      console.error(`Error disconnecting ${connection.platform}:`, error);
      toast.error(`Failed to disconnect ${connection.platform}`);
    } finally {
      setDisconnecting(null);
    }
  };

  const handleSharedToggle = async (profile: any) => {
    console.log("profile", profile);
    zero.mutate.ayrshare_user_profile.update({
      id: profile.id,
      values: {
        is_shared: !profile.is_shared
      }
    });
    toast.success(`Profile ${profile.is_shared ? 'made private' : 'made shared'} successfully`);
  };

  const handleDeleteProfile = async (profile: any) => {
    if (!confirm(`Are you sure you want to delete the profile "${profile.title || 'Untitled'}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const result = await deleteSocialProfile(profile.id);
      if (result.status === 'success') {
        toast.success('Profile deleted successfully');
        zero.mutate.ayrshare_user_profile.delete({
          id: profile.id,
        });
        // await fetchProfiles(); // Refresh the list
      } else {
        toast.error('Failed to delete profile');
      }
    } catch (error) {
      console.error('Error deleting profile:', error);
      toast.error('Failed to delete profile');
    }
  };

  const renderConnectedAccount = (connection: PlatformConnection) => {
    const logoSrc = platformLogos[connection.platform as keyof typeof platformLogos] || '/images/default-social-logo.png';
    const platformName = platformNames[connection.platform as keyof typeof platformNames] || connection.platform;
    const isDisconnecting = disconnecting === `${connection.id}-${connection.platform}`;

    return (
      <div key={connection.id} className="flex items-center justify-between p-3 border rounded-lg">
        <div className="flex items-center space-x-3">
          <img 
            src={logoSrc}
            alt={`${platformName} Logo`} 
            width={connection.platform === 'linkedin' ? 32 : 24}
            height={connection.platform === 'linkedin' ? 32 : 24}
            className="object-contain" 
          />
          
          {connection.user_image && (
            <img 
              src={connection.user_image} 
              alt={`${connection.display_name}'s profile`} 
              className="w-8 h-8 rounded-full object-cover" 
            />
          )}
          
          <div>
            <p className="font-medium text-sm">{connection.display_name}</p>
            {connection.username && (
              <p className="text-xs text-muted-foreground">@{connection.username}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className={connection.is_connected ? "text-green-600 bg-green-50 text-xs" : "text-orange-600 bg-orange-50 text-xs"}>
            {connection.is_connected ? 'Connected' : 'Needs Refresh'}
          </Badge>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => handleDisconnectAccount(connection)}
            disabled={isDisconnecting}
            className="text-red-500 hover:text-red-700"
          >
            {isDisconnecting ? 'Disconnecting...' : 'Disconnect'}
          </Button>
        </div>
      </div>
    );
  };

  const renderProfileCard = (profile: any) => {
    const isOwner = profile.user_id === workspace.user.id;
    // Get platform connections for this profile
    const profileConnections = platformConnections?.filter(
      (connection: any) => connection.refId === profile.refId && connection.is_connected
    ) || [];

    return (
      <Card key={profile.id} className="w-full">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <span>{profile.profile_name || 'Untitled Profile'}</span>
                {(profile.is_shared && isOwner ) ? (
                  <Badge variant="outline" className="text-xs">
                    <Share2 className="w-3 h-3 mr-1" />
                    Shared
                  </Badge>
                ) : 
                <Badge variant="outline" className="text-xs">
                  <Lock className="w-3 h-3 mr-1" />
                  Private
                </Badge>
              }
                {!isOwner && (
                  <Badge variant="secondary" className="text-xs">
                    <Users className="w-3 h-3 mr-1" />
                    Team Profile
                  </Badge>
                )}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Created {new Date(profile.created_at).toLocaleDateString()}
              </p>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleConnectAccounts(profile)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Manage Accounts
                </DropdownMenuItem>
              { isOwner && (
                <DropdownMenuItem onClick={() => handleSharedToggle(profile)}>
                  <Edit className="w-4 h-4 mr-2" />
                  {profile.is_shared ? 'Make Private' : 'Make Shared'}
                </DropdownMenuItem>
              )}
                {isOwner && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => handleDeleteProfile(profile)}
                      className="text-red-600"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Profile
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        
        <CardContent>
          {profileConnections.length > 0 ? (
            <div className="space-y-3">
              <p className="text-sm font-medium">Connected Accounts ({profileConnections.length})</p>
              <div className="space-y-2">
                {profileConnections.map((connection: PlatformConnection) => renderConnectedAccount(connection))}
              </div>
            </div>
          ) : (
            <div className="text-center py-6">
              <p className="text-muted-foreground mb-4">No social accounts connected yet</p>
              <Button onClick={() => handleConnectAccounts(profile)}>
                Connect Social Accounts
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (profiles.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
          <Share2 className="w-12 h-12 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-medium mb-2">
          <Trans i18nKey="integrations:socialProfiles.noProfiles" defaults="No Social Profiles Yet" />
        </h3>
        <p className="text-muted-foreground mb-4">
          <Trans 
            i18nKey="integrations:socialProfiles.noProfilesDescription" 
            defaults="Create your first social profile to start connecting your social media accounts." 
          />
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">
          Your Social Profiles ({profiles.length})
        </h3>
      </div>
      
      <div className="space-y-4">
        {profiles.map(renderProfileCard)}
      </div>
    </div>
  );
} 