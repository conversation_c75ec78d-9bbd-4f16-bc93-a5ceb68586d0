import { enhance<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@kit/next/routes';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { createHmac } from 'crypto';

/**
 * Fetch profile details from Ayrshare API
 */
async function getProfilesDetails(profileKey: string) {
  try {
    const displayInfoRaw = await fetch(`https://api.ayrshare.com/api/user`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${process.env.AYRSHARE_API_KEY}`,
        "Profile-Key": profileKey
      },
    });
    const displayInfo = await displayInfoRaw.json();
    if (displayInfo.activeSocialAccounts) {
      return displayInfo;
    } else {
      return {};
    }
  } catch (error) {
    return {};
  }
}

/**
 * Interface for Ayrshare Social Action Webhook payload
 */
interface AyrshareSocialActionWebhook {
  action: 'social';
  created: string;
  displayName?: string;
  hookId: string;
  platform: string;
  timeStamp: number;
  refId: string;
  refreshBy?: string;
  source: 'system' | 'user';
  title: string;
  type: 'link' | 'unlink' | 'refresh';
  url: string;
  details?: {
    status: string;
    code: number;
    message: string;
  };
}

/**
 * Verify HMAC signature for webhook security
 */
function verifyHmacSignature(
  body: string,
  signature: string,
  timestamp: string,
  secret: string
): boolean {
  if (!secret) {
    // If no secret is configured, skip verification
    return true;
  }

  try {
    const hmac = createHmac('sha256', secret);
    hmac.update(body);
    const calculatedSignature = hmac.digest('hex');
    
    return calculatedSignature === signature;
  } catch (error) {
    return false;
  }
}

/**
 * @description Handle Ayrshare social action webhooks
 */
export const POST = enhanceRouteHandler(
  async ({ request }) => {
    const logger = await getLogger();

    const ctx = {
      name: 'ayrshare-social-action.webhook'
    };


    try {
      // Get the request body as text for HMAC verification
      const rawBody = await request.text();

      // Parse the webhook payload
      let webhookData: AyrshareSocialActionWebhook;
      try {
        webhookData = JSON.parse(rawBody);

      } catch (parseError) {
        return new Response('Invalid JSON payload', { status: 400 });
      }

      // Verify webhook signature if secret is configured
      const secret = process.env.AYRSHARE_WEBHOOK_SECRET;
      if (secret) {
        const signature = request.headers.get('x-authorization-content-sha256');
        const timestamp = request.headers.get('x-authorization-timestamp');

        if (!signature || !timestamp) {
          logger.error(ctx, `Missing HMAC headers for webhook verification`);
          return new Response('Missing HMAC headers', { status: 401 });
        }

        if (!verifyHmacSignature(rawBody, signature, timestamp, secret)) {
          logger.error(ctx, `HMAC verification failed for webhook`);
          return new Response('HMAC verification failed', { status: 401 });
        }

        logger.info(ctx, `HMAC verification successful`);
      } else {
        logger.warn(ctx, `No webhook secret configured - skipping HMAC verification`);
      }

      // Log the webhook data for debugging
      logger.info({ ...ctx, webhookData }, `Ayrshare social action webhook received`);

      // Handle different webhook types
      switch (webhookData.action) {
        case 'social':
          await handleSocialActionWebhook(webhookData, logger, ctx);
          break;
        default:
          logger.warn({ ...ctx, action: webhookData.action }, `Unhandled webhook action type`);
      }

      logger.info(ctx, `Successfully processed Ayrshare webhook`);
      return new Response('OK', { status: 200 });

    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to process Ayrshare webhook`);
      return new Response('Failed to process webhook', { status: 500 });
    }
  },
  {
    auth: false,
  },
);

/**
 * Handle social action webhook (link, unlink, refresh)
 */
async function handleSocialActionWebhook(
  webhook: AyrshareSocialActionWebhook,
  logger: any,
  ctx: any
) {
  const client = getSupabaseServerAdminClient();

  logger.info({ 
    ...ctx, 
    refId: webhook.refId,
    platform: webhook.platform,
    type: webhook.type,
    source: webhook.source,
    title: webhook.title
  }, `Processing social action: ${webhook.type} ${webhook.platform}`);

  // Log detailed webhook information
  const webhookDetails = {
    hookId: webhook.hookId,
    platform: webhook.platform,
    type: webhook.type,
    source: webhook.source,
    displayName: webhook.displayName,
    title: webhook.title,
    refId: webhook.refId,
    created: webhook.created,
    timeStamp: webhook.timeStamp,
    refreshBy: webhook.refreshBy,
    url: webhook.url,
    details: webhook.details
  };

  logger.info({ ...ctx, webhookDetails }, `Social action webhook details`);

  try {
    // Find the profile by refId in the ayrshare_user_profile table
    const { data: userProfile, error: userProfileError } = await (client as any)
      .from('ayrshare_user_profile')
      .select('*')
      .eq('refId', webhook.refId)
      .single();

    if (userProfileError || !userProfile) {
      logger.error({ ...ctx, refId: webhook.refId, error: userProfileError },
        `❌ No user profile found for refId: ${webhook.refId}`);
      
      // Try to find profile by profileKey from the webhook title or other means
      // For now, let's try to create a basic profile record if none exists
      logger.warn({ ...ctx, refId: webhook.refId }, 
        `Attempting to handle webhook without existing profile record`);
      
      // We'll still process the webhook but with limited functionality
      // The user will need to create a proper profile first
      return;
    }


    // Handle different social action types
    switch (webhook.type) {
      case 'link': {
        logger.info({ ...ctx, platform: webhook.platform, refId: webhook.refId }, 
          `Social account linked: ${webhook.platform} for profile ${webhook.refId}`);
        
        if (webhook.displayName) {
          logger.info({ ...ctx, displayName: webhook.displayName }, 
            `Account display name: ${webhook.displayName}`);
        }

        // For link events, fetch full updated profile details from Ayrshare API to get platform-specific info
        const profileDetails = await getProfilesDetails(userProfile.profileKey);

        if (profileDetails.displayNames) {
          // Find the specific platform data from the API response
          const platformData = profileDetails.displayNames.find((item: any) => item.platform === webhook.platform);
          
          if (platformData) {
            logger.info({ ...ctx, platformData }, 
              `Found platform data for ${webhook.platform}`);

            // Insert/update individual platform connection
            const socialProfileData = {
              refId: userProfile.refId,
              platform: webhook.platform,
              display_name: platformData.displayName || webhook.displayName,
              username: platformData.username,
              user_image: platformData.userImage,
              profile_url: platformData.profileUrl,
              headline: platformData.headline,
              subscription_type: platformData.subscriptionType,
              verified_type: platformData.verifiedType,
              refresh_days_remaining: platformData.refreshDaysRemaining,
              refresh_required: platformData.refreshRequired ? new Date(platformData.refreshRequired) : null,
              is_connected: true,
              connected_at: new Date(),
              is_shared: userProfile.is_shared,
              user_id: userProfile.user_id,
              company_id: userProfile.company_id,
              profile_key: userProfile.profileKey,
            };


            const { data: socialProfile, error: upsertError } = await (client as any)
              .from('ayrshare_social_profiles')
              .upsert(socialProfileData, {
                onConflict: 'refId,platform,user_id,company_id'
              })
              .select()
              .single();

            if (upsertError) {
              logger.error({ ...ctx, error: upsertError, socialProfileData },
                `❌ Failed to insert platform connection for link event`);
            } else {
              logger.info({
                ...ctx,
                platformConnectionId: socialProfile?.id,
                socialProfile
              }, `✅ Successfully created/updated platform connection for link event`);
            }
          } else {
            logger.warn({ ...ctx, platform: webhook.platform }, 
              `Platform data not found in API response for link event`);
            
            // Still create a basic record with webhook data
            const { data: socialProfile, error: upsertError } = await (client as any)
              .from('ayrshare_social_profiles')
              .upsert({
                refId: userProfile.refId,
                platform: webhook.platform,
                display_name: webhook.displayName,
                is_connected: true,
                connected_at: new Date(),
                is_shared: userProfile.is_shared,
                user_id: userProfile.user_id,
                company_id: userProfile.company_id,
              }, {
                onConflict: 'refId,platform,user_id,company_id'
              })
              .select()
              .single();

            if (upsertError) {
              logger.error({ ...ctx, error: upsertError }, 
                `Failed to insert basic platform connection for link event`);
            } else {
              logger.info({ ...ctx, platformConnectionId: socialProfile.id }, 
                `Successfully created basic platform connection for link event`);
            }
          }
        } else {
          logger.warn({ ...ctx, profileKey: userProfile.profileKey }, 
            `No display names found in profile details for link event`);
        }
        break;
      }

      case 'unlink': {
        logger.info({ ...ctx, platform: webhook.platform, refId: webhook.refId }, 
          `Social account unlinked: ${webhook.platform} for profile ${webhook.refId}`);
        
        if (webhook.source === 'system') {
          logger.warn({ ...ctx, details: webhook.details }, 
            `System-initiated unlink - user may need to reconnect`);
        }

        // For unlink events, either delete the platform connection or mark as disconnected
        const { data: deletedConnection, error: deleteError } = await (client as any)
          .from('ayrshare_social_profiles')
          .delete()
          .eq('refId', userProfile.refId)
          .eq('platform', webhook.platform)
          .eq('user_id', userProfile.user_id)
          .eq('company_id', userProfile.company_id)
          .select()
          .single();

        if (deleteError) {
          logger.error({ ...ctx, error: deleteError }, 
            `Failed to delete platform connection for unlink event`);
          
          // Alternative: mark as disconnected instead of deleting
          const { data: updatedConnection, error: updateError } = await (client as any)
            .from('ayrshare_social_profiles')
            .update({
              is_connected: false,
              updated_at: new Date(),
            })
            .eq('refId', userProfile.refId)
            .eq('platform', webhook.platform)
            .eq('user_id', userProfile.user_id)
            .eq('company_id', userProfile.company_id)
            .select()
            .single();

          if (updateError) {
            logger.error({ ...ctx, error: updateError }, 
              `Failed to mark platform connection as disconnected for unlink event`);
          } else {
            logger.info({ ...ctx, platformConnectionId: updatedConnection.id }, 
              `Successfully marked platform connection as disconnected for unlink event`);
          }
        } else {
          logger.info({ ...ctx, deletedConnectionId: deletedConnection.id }, 
            `Successfully deleted platform connection for unlink event`);
        }
        break;
      }

      case 'refresh': {
        logger.info({ ...ctx, platform: webhook.platform, refId: webhook.refId }, 
          `Social account requires refresh: ${webhook.platform} for profile ${webhook.refId}`);
        
        if (webhook.refreshBy) {
          logger.info({ ...ctx, refreshBy: webhook.refreshBy }, 
            `Account must be refreshed by: ${webhook.refreshBy}`);
        }

        // For refresh events, update the refresh_required field for the platform
        const { data: refreshedConnection, error: refreshError } = await (client as any)
          .from('ayrshare_social_profiles')
          .update({
            refresh_required: webhook.refreshBy ? new Date(webhook.refreshBy) : null,
            updated_at: new Date(),
          })
          .eq('refId', userProfile.refId)
          .eq('platform', webhook.platform)
          .eq('user_id', userProfile.user_id)
          .eq('company_id', userProfile.company_id)
          .select()
          .single();

        if (refreshError) {
          logger.error({ ...ctx, error: refreshError }, 
            `Failed to update refresh requirements for platform connection`);
        } else {
          logger.info({ ...ctx, platformConnectionId: refreshedConnection.id }, 
            `Successfully updated refresh requirements for platform connection`);
        }
        break;
      }

      default:
        logger.warn({ ...ctx, type: webhook.type }, `Unknown social action type`);
    }

  } catch (error) {
    logger.error({ ...ctx, error }, `Error processing social action webhook`);
  }
}
