'use client';
import { useEffect, useMemo, useState } from 'react';
import { Zero } from '@rocicorp/zero';
import { createUseZero } from '@rocicorp/zero/react';
import { schema } from '@kit/zero-schema';
import { useUser, useUserSession } from '@kit/supabase/hooks/use-user';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';

import { createMutators } from '@kit/zero-schema';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

// Create a typed useZero hook
export const useZero = createUseZero<typeof schema, ReturnType<typeof createMutators>>();

// Custom hook to create and manage Zero instance
export function useZeroInstance() {
  const workspace = useTeamAccountWorkspace();
  const userId = workspace.user?.id;
  const supabase = useSupabase();
  
  // Get fresh session from browser client instead of server-side session
  const [session, setSession] = useState<any>(null);
  
  useEffect(() => {
    const getSession = async () => {
      const { data: { session: freshSession } } = await supabase.auth.getSession();
      console.log('🔍 Fresh session from browser:', freshSession?.user?.id, freshSession?.access_token?.substring(0, 20) + '...');
      
      // Clear any cached Zero data if session changed
      if (freshSession?.user?.id !== userId) {
        console.log('🧹 Clearing Zero cache due to user change');
        // Clear any Zero-related localStorage
        Object.keys(localStorage).forEach(key => {
          if (key.includes('zero') || key.includes('rocicorp')) {
            localStorage.removeItem(key);
          }
        });
      }
      
      setSession(freshSession);
    };
    
    getSession();
    
    // Listen for auth state changes to update session
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('🔄 Auth state changed:', event, session?.user?.id, session?.access_token?.substring(0, 20) + '...');
      
      // Clear Zero cache on sign out
      if (event === 'SIGNED_OUT') {
        console.log('🧹 Clearing Zero cache on sign out');
        Object.keys(localStorage).forEach(key => {
          if (key.includes('zero') || key.includes('rocicorp')) {
            localStorage.removeItem(key);
          }
        });
      }
      
      setSession(session);
    });
    
    return () => subscription.unsubscribe();
  }, [supabase.auth, userId]);

  const z =
   useMemo(() => {
    // Only create Zero instance if we have required data
    if (!userId || !session?.access_token) {
      console.log('❌ Zero instance not created - missing data:', { userId, hasToken: !!session?.access_token });
      return null;
    }
    
    console.log('✅ Creating Zero instance with:', { 
      userId, 
      tokenStart: session.access_token.substring(0, 20) + '...',
      server: process.env.NEXT_PUBLIC_ZERO_SERVER 
    });
    
    return new Zero({
      userID: userId,
      auth: session.access_token,
      server: process.env.NEXT_PUBLIC_ZERO_SERVER,
      mutators: createMutators(),
      schema,
    });

  }, [userId, session?.access_token])
  
  // Cleanup Zero instance when component unmounts
  useEffect(() => {
    return () => {
      if (z) {
        console.log('🧹 Cleaning up Zero instance');
        z.close();
      }
    };
  }, [z]);
  
  // Add debugging function to window for manual cache clearing
  useEffect(() => {
    (window as any).clearZeroCache = () => {
      console.log('🧹 Manual Zero cache clear triggered');
      Object.keys(localStorage).forEach(key => {
        if (key.includes('zero') || key.includes('rocicorp')) {
          console.log('Removing:', key);
          localStorage.removeItem(key);
        }
      });
      // Also clear sessionStorage
      Object.keys(sessionStorage).forEach(key => {
        if (key.includes('zero') || key.includes('rocicorp')) {
          console.log('Removing from sessionStorage:', key);
          sessionStorage.removeItem(key);
        }
      });
      console.log('✅ Zero cache cleared. Please refresh the page.');
    };
  }, []);
  
  return z;
}
